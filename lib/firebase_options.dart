// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyCOJ-wzPwyPU7NxSD_fytUcWK_uF1nBIAg',
    appId: '1:839919136965:web:9d70f7486feb236ab540ac',
    messagingSenderId: '839919136965',
    projectId: 'task-4dccc',
    authDomain: 'task-4dccc.firebaseapp.com',
    storageBucket: 'task-4dccc.firebasestorage.app',
    measurementId: 'G-RT699T81VF',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyD3fog8Rp_CERT2vtgzsPzDeEA8UjgaceQ',
    appId: '1:839919136965:android:6bd9dae05864fdfdb540ac',
    messagingSenderId: '839919136965',
    projectId: 'task-4dccc',
    storageBucket: 'task-4dccc.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBQ9AXLFYy75cmTQa1brx1Iq2V1q2qNh8U',
    appId: '1:839919136965:ios:ba550082bd6c9b52b540ac',
    messagingSenderId: '839919136965',
    projectId: 'task-4dccc',
    storageBucket: 'task-4dccc.firebasestorage.app',
    iosBundleId: 'com.app.task',
  );
}
