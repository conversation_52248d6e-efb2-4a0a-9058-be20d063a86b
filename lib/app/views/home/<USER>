import 'package:get/get.dart';
import '../../services/auth_service.dart';
import '../../services/database_service.dart';
import '../../services/localization_service.dart';
import '../../services/firebase_service.dart';
import '../../models/user_model.dart';
import '../../models/message_model.dart';
import '../../routes/app_routes.dart';

class HomeController extends GetxController {
  late AuthService _authService;
  late DatabaseService _databaseService;
  late LocalizationService _localizationService;
  late FirebaseService _firebaseService;

  final RxList<Map<String, dynamic>> chatRooms = <Map<String, dynamic>>[].obs;
  final RxBool isLoading = false.obs;
  
  UserModel? get currentUser => _authService.currentUser;
  
  @override
  void onInit() {
    super.onInit();
    _authService = Get.find<AuthService>();
    _databaseService = Get.find<DatabaseService>();
    _localizationService = Get.find<LocalizationService>();
    _firebaseService = Get.find<FirebaseService>();
    _loadChatRooms();
  }
  
  Future<void> _loadChatRooms() async {
    if (currentUser == null) return;
    
    try {
      isLoading.value = true;
      final rooms = await _databaseService.getChatRooms(currentUser!.id!);
      chatRooms.value = rooms;
    } catch (e) {
      print('Error loading chat rooms: $e');
    } finally {
      isLoading.value = false;
    }
  }
  
  void goToChat(String userId, String userName) {
    Get.toNamed(Routes.CHAT, arguments: {
      'userId': userId,
      'userName': userName,
    });
  }
  
  void goToMap() {
    Get.toNamed(Routes.MAP);
  }
  
  void goToQRScanner() {
    Get.toNamed(Routes.QR_SCANNER);
  }
  
  void goToProfile() {
    Get.toNamed(Routes.PROFILE);
  }
  
  void changeLanguage() {
    final currentLang = _localizationService.getCurrentLanguageCode();
    final newLang = currentLang == 'en' ? 'ar' : 'en';
    _localizationService.changeLanguage(newLang);
  }
  
  Future<void> logout() async {
    await _authService.logout();
    Get.offAllNamed(Routes.LOGIN);
  }
  
  void refreshChatRooms() {
    _loadChatRooms();
  }

  /// Test method to simulate receiving a message from another user
  Future<void> testIncomingMessage() async {
    if (currentUser == null) return;

    // Create a test user if it doesn't exist
    final testUserId = 'test_user_123';
    final testUser = await _databaseService.getUserById(testUserId);

    if (testUser == null) {
      // Create test user
      final newTestUser = UserModel(
        id: testUserId,
        email: '<EMAIL>',
        displayName: 'Test User',
        country: 'Test Country',
        mobile: '+1234567890',
        fcmToken: 'test_token_123', // Fake token for testing
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      await _databaseService.insertUser(newTestUser);
      print('Created test user: ${newTestUser.displayName}');
    }

    // Simulate a message from the test user
    await _firebaseService.simulateIncomingMessage(
      fromUserId: testUserId,
      fromUserName: 'Test User',
      messageContent: 'Hello! This is a test message from another user.',
    );

    // Refresh chat rooms to show the new message
    refreshChatRooms();
  }

  /// Test method to send a real FCM notification to the test user
  Future<void> testSendNotification() async {
    if (currentUser == null) return;

    final testUserId = 'test_user_123';

    // Send a real FCM notification to the test user
    final success = await _firebaseService.sendNotificationToUser(
      targetUserId: testUserId,
      messageContent: 'This is a real FCM test message!',
    );

    if (success) {
      print('FCM notification sent successfully to test user');
    } else {
      print('Failed to send FCM notification to test user');
    }
  }
}
