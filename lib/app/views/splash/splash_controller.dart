import 'package:get/get.dart';
import '../../services/auth_service.dart';
import '../../routes/app_routes.dart';

class SplashController extends GetxController {
  late AuthService _authService;
  
  @override
  void onInit() {
    super.onInit();
    _authService = Get.find<AuthService>();
    _navigateToNextScreen();
  }
  
  void _navigateToNextScreen() async {
    // Wait for 2 seconds to show splash screen
    await Future.delayed(const Duration(seconds: 2));
    
    // Check if user is logged in
    if (_authService.isLoggedIn) {
      Get.offAllNamed(Routes.HOME);
    } else {
      Get.offAllNamed(Routes.LOGIN);
    }
  }
}
