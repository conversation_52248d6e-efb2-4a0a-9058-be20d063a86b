import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:permission_handler/permission_handler.dart';
import '../../services/auth_service.dart';
import '../../services/firebase_service.dart';
import '../../services/database_service.dart';
import '../../models/user_model.dart';
import '../../routes/app_routes.dart';

class QrScannerController extends GetxController {
  late MobileScannerController scannerController;
  
  final RxBool isFlashOn = false.obs;
  final RxBool isScanning = true.obs;
  final RxString scannedData = ''.obs;
  
  late AuthService _authService;
  late FirebaseService _firebaseService;
  late DatabaseService _databaseService;
  
  @override
  void onInit() {
    super.onInit();
    _authService = Get.find<AuthService>();
    _firebaseService = Get.find<FirebaseService>();
    _databaseService = Get.find<DatabaseService>();
    scannerController = MobileScannerController();
    _requestCameraPermission();
  }

  @override
  void onClose() {
    scannerController.dispose();
    super.onClose();
  }
  
  Future<void> _requestCameraPermission() async {
    final status = await Permission.camera.request();
    if (status != PermissionStatus.granted) {
      Get.snackbar(
        'error'.tr,
        'Camera permission is required to scan QR codes',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      Get.back();
    }
  }
  
  void onDetect(BarcodeCapture capture) {
    final List<Barcode> barcodes = capture.barcodes;
    if (isScanning.value && barcodes.isNotEmpty) {
      final barcode = barcodes.first;
      if (barcode.rawValue != null) {
        _handleScannedData(barcode.rawValue!);
      }
    }
  }
  
  Future<void> _handleScannedData(String data) async {
    isScanning.value = false;
    scannedData.value = data;

    try {
      print('Scanned QR data: $data');

      // Parse the scanned QR code data
      // Expected format: "chat_user:{userId}:{userName}"
      if (data.startsWith('chat_user:')) {
        final parts = data.split(':');
        if (parts.length >= 3) {
          final userId = parts[1];
          final userName = parts[2];

          print('Parsed QR: userId=$userId, userName=$userName');

          await _connectWithUser(userId, userName);
        } else {
          _showError('Invalid QR code format');
        }
      } else if (data.startsWith('chat_token:')) {
        // Support legacy format for backward compatibility
        final parts = data.split(':');
        if (parts.length >= 4) {
          final deviceToken = parts[1];
          final userId = parts[2];
          final userName = parts[3];

          print('Parsed legacy QR: deviceToken=$deviceToken, userId=$userId, userName=$userName');
          print('Device Token Length: ${deviceToken.length}');

          await _connectWithUserLegacy(deviceToken, userId, userName);
        } else {
          _showError('Invalid QR code format');
        }
      } else {
        _showError('This is not a valid chat QR code');
      }
    } catch (e) {
      _showError('Error processing QR code: $e');
      print('Error in _handleScannedData: $e');
    }
    
    // Resume scanning after 2 seconds
    Future.delayed(const Duration(seconds: 2), () {
      if (!Get.isRegistered<QrScannerController>()) return;
      isScanning.value = true;
    });
  }
  
  Future<void> _connectWithUser(String userId, String userName) async {
    try {
      // Create a chat room between current user and scanned user
      final currentUser = _authService.currentUser;
      if (currentUser == null) {
        _showError('You must be logged in to connect with other users');
        return;
      }

      // Check if trying to connect with yourself
      if (currentUser.id == userId) {
        _showError('You cannot connect with yourself!');
        return;
      }

      // Check if user already exists in database
      final existingUser = await _databaseService.getUserById(userId);
      if (existingUser == null) {
        // Create the scanned user in the database with placeholder FCM token
        // The actual FCM token will be fetched when sending notifications
        final scannedUser = UserModel(
          id: userId,
          email: '$<EMAIL>', // Placeholder email
          displayName: userName,
          country: 'Unknown',
          mobile: 'Unknown',
          fcmToken: null, // Will be fetched dynamically when needed
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        await _databaseService.insertUser(scannedUser);
        print('Added scanned user to database: $userName ($userId)');
      } else {
        // Update the existing user's display name if needed
        final updatedUser = existingUser.copyWith(
          displayName: userName,
          updatedAt: DateTime.now(),
        );
        await _databaseService.updateUser(updatedUser);
        print('Updated existing user info: $userName ($userId)');
      }

      // Create chat room in database
      await _databaseService.createChatRoom(currentUser.id!, userId);

      // Show success message
      Get.snackbar(
        'success'.tr,
        'Connected with $userName! You can now start chatting.',
        backgroundColor: Colors.green,
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );

      // Navigate to chat screen
      Get.offNamed(Routes.CHAT, arguments: {
        'userId': userId,
        'userName': userName,
      });

    } catch (e) {
      _showError('Failed to connect with user: $e');
      print('Error in _connectWithUser: $e');
    }
  }

  // Legacy method for backward compatibility with old QR codes
  Future<void> _connectWithUserLegacy(String deviceToken, String userId, String userName) async {
    try {
      // Create a chat room between current user and scanned user
      final currentUser = _authService.currentUser;
      if (currentUser == null) {
        _showError('You must be logged in to connect with other users');
        return;
      }

      // Check if trying to connect with yourself
      if (currentUser.id == userId) {
        _showError('You cannot connect with yourself!');
        return;
      }

      // Check if user already exists in database
      final existingUser = await _databaseService.getUserById(userId);
      if (existingUser == null) {
        // Create the scanned user in the database with the FCM token from QR
        final scannedUser = UserModel(
          id: userId,
          email: '$<EMAIL>', // Placeholder email
          displayName: userName,
          country: 'Unknown',
          mobile: 'Unknown',
          fcmToken: deviceToken,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        await _databaseService.insertUser(scannedUser);
        print('Added scanned user to database (legacy): $userName ($userId)');
        print('Stored FCM token length: ${scannedUser.fcmToken?.length ?? 0}');
      } else {
        // Update the existing user's FCM token
        final updatedUser = existingUser.copyWith(
          fcmToken: deviceToken,
          updatedAt: DateTime.now(),
        );
        await _databaseService.updateUser(updatedUser);
        print('Updated existing user FCM token (legacy): $userName ($userId)');
      }

      // Create chat room in database
      await _databaseService.createChatRoom(currentUser.id!, userId);

      // Show success message
      Get.snackbar(
        'success'.tr,
        'Connected with $userName! You can now start chatting.',
        backgroundColor: Colors.green,
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );

      // Navigate to chat screen
      Get.offNamed(Routes.CHAT, arguments: {
        'userId': userId,
        'userName': userName,
        'deviceToken': deviceToken,
      });

    } catch (e) {
      _showError('Failed to connect with user: $e');
      print('Error in _connectWithUserLegacy: $e');
    }
  }
  
  void _showError(String message) {
    Get.snackbar(
      'error'.tr,
      message,
      backgroundColor: Colors.red,
      colorText: Colors.white,
      duration: const Duration(seconds: 3),
    );
  }
  
  void toggleFlash() async {
    await scannerController.toggleTorch();
    isFlashOn.value = !isFlashOn.value;
  }

  void flipCamera() async {
    await scannerController.switchCamera();
  }

  void pauseCamera() async {
    await scannerController.stop();
  }

  void resumeCamera() async {
    await scannerController.start();
  }
  
  void goBack() {
    Get.back();
  }
  
  void goToQRGenerator() {
    Get.toNamed(Routes.QR_GENERATOR);
  }
}
