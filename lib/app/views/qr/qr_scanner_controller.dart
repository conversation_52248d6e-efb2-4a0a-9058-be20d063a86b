import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:permission_handler/permission_handler.dart';
import '../../services/auth_service.dart';
import '../../services/firebase_service.dart';
import '../../services/database_service.dart';
import '../../models/user_model.dart';
import '../../routes/app_routes.dart';

class QrScannerController extends GetxController {
  late MobileScannerController scannerController;
  
  final RxBool isFlashOn = false.obs;
  final RxBool isScanning = true.obs;
  final RxString scannedData = ''.obs;
  
  late AuthService _authService;
  late FirebaseService _firebaseService;
  late DatabaseService _databaseService;
  
  @override
  void onInit() {
    super.onInit();
    _authService = Get.find<AuthService>();
    _firebaseService = Get.find<FirebaseService>();
    _databaseService = Get.find<DatabaseService>();
    scannerController = MobileScannerController();
    _requestCameraPermission();
  }

  @override
  void onClose() {
    scannerController.dispose();
    super.onClose();
  }
  
  Future<void> _requestCameraPermission() async {
    final status = await Permission.camera.request();
    if (status != PermissionStatus.granted) {
      Get.snackbar(
        'error'.tr,
        'Camera permission is required to scan QR codes',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      Get.back();
    }
  }
  
  void onDetect(BarcodeCapture capture) {
    final List<Barcode> barcodes = capture.barcodes;
    if (isScanning.value && barcodes.isNotEmpty) {
      final barcode = barcodes.first;
      if (barcode.rawValue != null) {
        _handleScannedData(barcode.rawValue!);
      }
    }
  }
  
  Future<void> _handleScannedData(String data) async {
    isScanning.value = false;
    scannedData.value = data;

    try {
      print('Scanned QR data: $data');

      // Parse the scanned QR code data
      // Preferred format: "chat_token:{deviceToken}:{userId}:{userName}"
      if (data.startsWith('chat_token:')) {
        final parts = data.split(':');
        if (parts.length >= 4) {
          final deviceToken = parts[1];
          final userId = parts[2];
          final userName = parts[3];

          print('Parsed QR with token: deviceToken=$deviceToken, userId=$userId, userName=$userName');
          print('Device Token Length: ${deviceToken.length}');

          await _connectWithUserWithToken(deviceToken, userId, userName);
        } else {
          _showError('Invalid QR code format');
        }
      } else if (data.startsWith('chat_user:')) {
        // Fallback format without token: "chat_user:{userId}:{userName}"
        final parts = data.split(':');
        if (parts.length >= 3) {
          final userId = parts[1];
          final userName = parts[2];

          print('Parsed QR without token: userId=$userId, userName=$userName');

          await _connectWithUserWithoutToken(userId, userName);
        } else {
          _showError('Invalid QR code format');
        }
      } else {
        _showError('This is not a valid chat QR code');
      }
    } catch (e) {
      _showError('Error processing QR code: $e');
      print('Error in _handleScannedData: $e');
    }
    
    // Resume scanning after 2 seconds
    Future.delayed(const Duration(seconds: 2), () {
      if (!Get.isRegistered<QrScannerController>()) return;
      isScanning.value = true;
    });
  }
  
  Future<void> _connectWithUserWithoutToken(String userId, String userName) async {
    try {
      // Create a chat room between current user and scanned user
      final currentUser = _authService.currentUser;
      if (currentUser == null) {
        _showError('You must be logged in to connect with other users');
        return;
      }

      // Check if trying to connect with yourself
      if (currentUser.id == userId) {
        _showError('You cannot connect with yourself!');
        return;
      }

      // Check if user already exists in database
      final existingUser = await _databaseService.getUserById(userId);
      if (existingUser == null) {
        // Create the scanned user in the database
        // Note: We can't get their actual FCM token, so we'll use a placeholder
        // In a real app, this would be handled server-side
        final scannedUser = UserModel(
          id: userId,
          email: '$<EMAIL>', // Placeholder email
          displayName: userName,
          country: 'Unknown',
          mobile: 'Unknown',
          fcmToken: 'placeholder_token_$userId', // Placeholder for now
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        await _databaseService.insertUser(scannedUser);
        print('Added scanned user to database: $userName ($userId)');

        // Show a warning about FCM limitations
        Get.snackbar(
          'warning'.tr,
          'Connected with $userName! ⚠️ Notifications may not work - they need to scan your QR code too.',
          backgroundColor: Colors.orange,
          colorText: Colors.white,
          duration: const Duration(seconds: 5),
        );
      } else {
        // Update the existing user's display name if needed
        final updatedUser = existingUser.copyWith(
          displayName: userName,
          updatedAt: DateTime.now(),
        );
        await _databaseService.updateUser(updatedUser);
        print('Updated existing user info: $userName ($userId)');
      }

      // Create chat room in database
      await _databaseService.createChatRoom(currentUser.id!, userId);

      // Show success message
      Get.snackbar(
        'success'.tr,
        'Connected with $userName! You can now start chatting.',
        backgroundColor: Colors.green,
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );

      // Navigate to chat screen
      Get.offNamed(Routes.CHAT, arguments: {
        'userId': userId,
        'userName': userName,
      });

    } catch (e) {
      _showError('Failed to connect with user: $e');
      print('Error in _connectWithUser: $e');
    }
  }

  // Primary method for QR codes with FCM tokens
  Future<void> _connectWithUserWithToken(String deviceToken, String userId, String userName) async {
    try {
      // Create a chat room between current user and scanned user
      final currentUser = _authService.currentUser;
      if (currentUser == null) {
        _showError('You must be logged in to connect with other users');
        return;
      }

      // Check if trying to connect with yourself
      if (currentUser.id == userId) {
        _showError('You cannot connect with yourself!');
        return;
      }

      // Check if user already exists in database
      final existingUser = await _databaseService.getUserById(userId);
      if (existingUser == null) {
        // Create the scanned user in the database with the FCM token from QR
        final scannedUser = UserModel(
          id: userId,
          email: '$<EMAIL>', // Placeholder email
          displayName: userName,
          country: 'Unknown',
          mobile: 'Unknown',
          fcmToken: deviceToken,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        await _databaseService.insertUser(scannedUser);
        print('Added scanned user to database with token: $userName ($userId)');
        print('Stored FCM token length: ${scannedUser.fcmToken?.length ?? 0}');
      } else {
        // Update the existing user's FCM token
        final updatedUser = existingUser.copyWith(
          fcmToken: deviceToken,
          updatedAt: DateTime.now(),
        );
        await _databaseService.updateUser(updatedUser);
        print('Updated existing user FCM token: $userName ($userId)');
      }

      // Create chat room in database
      await _databaseService.createChatRoom(currentUser.id!, userId);

      // Show success message
      Get.snackbar(
        'success'.tr,
        'Connected with $userName! You can now start chatting.',
        backgroundColor: Colors.green,
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );

      // Navigate to chat screen
      Get.offNamed(Routes.CHAT, arguments: {
        'userId': userId,
        'userName': userName,
        'deviceToken': deviceToken,
      });

    } catch (e) {
      _showError('Failed to connect with user: $e');
      print('Error in _connectWithUserWithToken: $e');
    }
  }
  
  void _showError(String message) {
    Get.snackbar(
      'error'.tr,
      message,
      backgroundColor: Colors.red,
      colorText: Colors.white,
      duration: const Duration(seconds: 3),
    );
  }
  
  void toggleFlash() async {
    await scannerController.toggleTorch();
    isFlashOn.value = !isFlashOn.value;
  }

  void flipCamera() async {
    await scannerController.switchCamera();
  }

  void pauseCamera() async {
    await scannerController.stop();
  }

  void resumeCamera() async {
    await scannerController.start();
  }
  
  void goBack() {
    Get.back();
  }
  
  void goToQRGenerator() {
    Get.toNamed(Routes.QR_GENERATOR);
  }
}
