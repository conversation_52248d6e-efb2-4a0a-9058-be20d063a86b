import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:qr_code_scanner/qr_code_scanner.dart';
import 'package:permission_handler/permission_handler.dart';
import '../../services/auth_service.dart';
import '../../services/firebase_service.dart';
import '../../services/database_service.dart';
import '../../models/user_model.dart';
import '../../routes/app_routes.dart';

class QrScannerController extends GetxController {
  final GlobalKey qrKey = GlobalKey(debugLabel: 'QR');
  QRViewController? qrController;
  
  final RxBool isFlashOn = false.obs;
  final RxBool isScanning = true.obs;
  final RxString scannedData = ''.obs;
  
  late AuthService _authService;
  late FirebaseService _firebaseService;
  late DatabaseService _databaseService;
  
  @override
  void onInit() {
    super.onInit();
    _authService = Get.find<AuthService>();
    _firebaseService = Get.find<FirebaseService>();
    _databaseService = Get.find<DatabaseService>();
    _requestCameraPermission();
  }
  
  @override
  void onClose() {
    qrController?.dispose();
    super.onClose();
  }
  
  Future<void> _requestCameraPermission() async {
    final status = await Permission.camera.request();
    if (status != PermissionStatus.granted) {
      Get.snackbar(
        'error'.tr,
        'Camera permission is required to scan QR codes',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      Get.back();
    }
  }
  
  void onQRViewCreated(QRViewController controller) {
    qrController = controller;
    controller.scannedDataStream.listen((scanData) {
      if (isScanning.value && scanData.code != null) {
        _handleScannedData(scanData.code!);
      }
    });
  }
  
  Future<void> _handleScannedData(String data) async {
    isScanning.value = false;
    scannedData.value = data;
    
    try {
      // Parse the scanned QR code data
      // Expected format: "chat_token:{deviceToken}:{userId}:{userName}"
      if (data.startsWith('chat_token:')) {
        final parts = data.split(':');
        if (parts.length >= 4) {
          final deviceToken = parts[1];
          final userId = parts[2];
          final userName = parts[3];
          
          await _connectWithUser(deviceToken, userId, userName);
        } else {
          _showError('Invalid QR code format');
        }
      } else {
        _showError('This is not a valid chat QR code');
      }
    } catch (e) {
      _showError('Error processing QR code: $e');
    }
    
    // Resume scanning after 2 seconds
    Future.delayed(const Duration(seconds: 2), () {
      if (!Get.isRegistered<QrScannerController>()) return;
      isScanning.value = true;
    });
  }
  
  Future<void> _connectWithUser(String deviceToken, String userId, String userName) async {
    try {
      // Create a chat room between current user and scanned user
      final currentUser = _authService.currentUser;
      if (currentUser == null) {
        _showError('You must be logged in to connect with other users');
        return;
      }
      
      // Create chat room in database
      await _databaseService.createChatRoom(currentUser.id!, userId);
      
      // Show success message
      Get.snackbar(
        'success'.tr,
        'Connected with $userName! You can now start chatting.',
        backgroundColor: Colors.green,
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );
      
      // Navigate to chat screen
      Get.offNamed(Routes.CHAT, arguments: {
        'userId': userId,
        'userName': userName,
        'deviceToken': deviceToken,
      });
      
    } catch (e) {
      _showError('Failed to connect with user: $e');
    }
  }
  
  void _showError(String message) {
    Get.snackbar(
      'error'.tr,
      message,
      backgroundColor: Colors.red,
      colorText: Colors.white,
      duration: const Duration(seconds: 3),
    );
  }
  
  void toggleFlash() async {
    if (qrController != null) {
      await qrController!.toggleFlash();
      isFlashOn.value = !isFlashOn.value;
    }
  }
  
  void flipCamera() async {
    if (qrController != null) {
      await qrController!.flipCamera();
    }
  }
  
  void pauseCamera() async {
    if (qrController != null) {
      await qrController!.pauseCamera();
    }
  }
  
  void resumeCamera() async {
    if (qrController != null) {
      await qrController!.resumeCamera();
    }
  }
  
  void goBack() {
    Get.back();
  }
  
  void goToQRGenerator() {
    Get.toNamed(Routes.QR_GENERATOR);
  }
}
