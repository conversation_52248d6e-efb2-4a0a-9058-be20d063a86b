import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'qr_scanner_controller.dart';

class QrScannerView extends GetView<QrScannerController> {
  const QrScannerView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('qr_scanner'.tr),
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: controller.goToQRGenerator,
            icon: const Icon(Icons.qr_code),
            tooltip: 'generate_qr'.tr,
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            flex: 4,
            child: Stack(
              children: [
                MobileScanner(
                  controller: controller.scannerController,
                  onDetect: controller.onDetect,
                ),

                // Custom overlay for scanning frame
                Center(
                  child: Container(
                    width: 250,
                    height: 250,
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: Theme.of(context).primaryColor,
                        width: 4,
                      ),
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                ),
                
                // Flash and flip camera controls
                Positioned(
                  top: 20,
                  right: 20,
                  child: Column(
                    children: [
                      Obx(() => FloatingActionButton(
                        mini: true,
                        onPressed: controller.toggleFlash,
                        backgroundColor: controller.isFlashOn.value
                            ? Colors.yellow
                            : Colors.white.withValues(alpha: 0.8),
                        child: Icon(
                          controller.isFlashOn.value 
                              ? Icons.flash_on 
                              : Icons.flash_off,
                          color: controller.isFlashOn.value 
                              ? Colors.black 
                              : Colors.black,
                        ),
                      )),
                      const SizedBox(height: 10),
                      FloatingActionButton(
                        mini: true,
                        onPressed: controller.flipCamera,
                        backgroundColor: Colors.white.withValues(alpha: 0.8),
                        child: const Icon(
                          Icons.flip_camera_ios,
                          color: Colors.black,
                        ),
                      ),
                    ],
                  ),
                ),
                
                // Scanning status indicator
                Positioned(
                  bottom: 20,
                  left: 0,
                  right: 0,
                  child: Obx(() => Container(
                    margin: const EdgeInsets.symmetric(horizontal: 20),
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: controller.isScanning.value
                          ? Colors.green.withValues(alpha: 0.8)
                          : Colors.orange.withValues(alpha: 0.8),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        if (controller.isScanning.value) ...[
                          const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Scanning for QR codes...',
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ] else ...[
                          const Icon(Icons.check_circle, color: Colors.white),
                          const SizedBox(width: 8),
                          Text(
                            'QR Code detected!',
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ],
                    ),
                  )),
                ),
              ],
            ),
          ),
          
          Expanded(
            flex: 1,
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              color: Colors.black,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'scan_to_connect'.tr,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Point your camera at another user\'s QR code to connect and start chatting.',
                    style: TextStyle(
                      color: Colors.grey[300],
                      fontSize: 14,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  
                  // Show scanned data if available
                  Obx(() => controller.scannedData.value.isNotEmpty
                      ? Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.grey[800],
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            'Last scanned: ${controller.scannedData.value}',
                            style: TextStyle(
                              color: Colors.grey[300],
                              fontSize: 12,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        )
                      : const SizedBox.shrink()),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
