import 'package:get/get.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:http/http.dart' as http;
import 'package:googleapis_auth/auth_io.dart';
import 'dart:convert';
import '../models/message_model.dart';
import 'auth_service.dart';
import 'database_service.dart';
import 'notification_service.dart';

class FirebaseService extends GetxService {
  late FirebaseMessaging _messaging;
  late AuthService _authService;
  late DatabaseService _databaseService;
  late NotificationService _notificationService;

  String? _deviceToken;
  String? get deviceToken => _deviceToken;

  // FCM v1 API Configuration
  // Replace with your Firebase project ID
  static const String _projectId = 'YOUR_PROJECT_ID_HERE';
  static const String _fcmScope = 'https://www.googleapis.com/auth/firebase.messaging';

  // Service Account JSON - In production, store this securely
  // You can get this from Firebase Console > Project Settings > Service Accounts > Generate new private key
  static const Map<String, dynamic> _serviceAccountJson = **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************;
  
  Future<FirebaseService> init() async {
    _messaging = FirebaseMessaging.instance;
    _authService = Get.find<AuthService>();
    _databaseService = Get.find<DatabaseService>();
    _notificationService = Get.find<NotificationService>();

    await _initializeMessaging();
    return this;
  }
  
  Future<void> _initializeMessaging() async {
    // Request permission for notifications
    NotificationSettings settings = await _messaging.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );
    
    if (settings.authorizationStatus == AuthorizationStatus.authorized) {
      print('User granted permission');
    } else if (settings.authorizationStatus == AuthorizationStatus.provisional) {
      print('User granted provisional permission');
    } else {
      print('User declined or has not accepted permission');
    }
    
    // Get the device token
    _deviceToken = await _messaging.getToken();
    print('Device Token: $_deviceToken');
    
    // Update user's device token
    if (_deviceToken != null && _authService.currentUser != null) {
      await _authService.updateDeviceToken(_deviceToken!);
    }
    
    // Handle foreground messages
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);
    
    // Handle background messages
    FirebaseMessaging.onBackgroundMessage(_handleBackgroundMessage);
    
    // Handle notification taps
    FirebaseMessaging.onMessageOpenedApp.listen(_handleNotificationTap);
    
    // Handle initial message if app was opened from notification
    RemoteMessage? initialMessage = await _messaging.getInitialMessage();
    if (initialMessage != null) {
      _handleNotificationTap(initialMessage);
    }
  }
  
  Future<void> _handleForegroundMessage(RemoteMessage message) async {
    print('Received foreground message: ${message.messageId}');
    await _processIncomingMessage(message);

    // Show in-app notification when app is in foreground
    if (message.data.containsKey('senderName') && message.data.containsKey('messageContent')) {
      _notificationService.showInAppNotification(
        title: message.data['senderName'] ?? 'New Message',
        message: message.data['messageContent'] ?? '',
        onTap: () {
          if (message.data.containsKey('senderId')) {
            Get.toNamed('/chat', arguments: {
              'userId': message.data['senderId'],
              'userName': message.data['senderName'] ?? 'Unknown',
            });
          }
        },
      );
    }
  }
  
  static Future<void> _handleBackgroundMessage(RemoteMessage message) async {
    print('Received background message: ${message.messageId}');
    // Handle background message processing here
  }
  
  Future<void> _handleNotificationTap(RemoteMessage message) async {
    print('Notification tapped: ${message.messageId}');
    // Navigate to chat screen or handle notification tap
    if (message.data.containsKey('senderId')) {
      // Navigate to chat with sender
      Get.toNamed('/chat', arguments: {
        'userId': message.data['senderId'],
        'userName': message.data['senderName'] ?? 'Unknown',
      });
    }
  }
  
  Future<void> _processIncomingMessage(RemoteMessage message) async {
    try {
      final data = message.data;
      
      if (data.containsKey('messageContent') && 
          data.containsKey('senderId') && 
          data.containsKey('receiverId')) {
        
        final messageModel = MessageModel(
          id: data['messageId'] ?? DateTime.now().millisecondsSinceEpoch.toString(),
          senderId: data['senderId'],
          receiverId: data['receiverId'],
          content: data['messageContent'],
          timestamp: DateTime.now(),
          status: MessageStatus.delivered,
        );
        
        // Save message to local database
        await _databaseService.insertMessage(messageModel);
        
        // Notify UI about new message
        Get.find<DatabaseService>(); // This will trigger UI updates through GetX
      }
    } catch (e) {
      print('Error processing incoming message: $e');
    }
  }

  /// Get OAuth 2.0 access token for FCM v1 API
  Future<String?> _getAccessToken() async {
    try {
      // Check if service account is configured
      if (_serviceAccountJson['project_id'] == 'YOUR_PROJECT_ID') {
        print('Service account not configured. Please add your service account JSON.');
        return null;
      }

      final accountCredentials = ServiceAccountCredentials.fromJson(_serviceAccountJson);
      final scopes = [_fcmScope];

      final authClient = await clientViaServiceAccount(accountCredentials, scopes);
      final accessToken = authClient.credentials.accessToken.data;
      authClient.close();

      return accessToken;
    } catch (e) {
      print('Error getting access token: $e');
      return null;
    }
  }

  Future<bool> sendPushNotification({
    required String targetToken,
    required String senderName,
    required String messageContent,
    required String senderId,
    required String receiverId,
    String? messageId,
  }) async {
    try {
      // Get OAuth 2.0 access token
      final accessToken = await _getAccessToken();
      if (accessToken == null) {
        print('Could not get access token. Falling back to local notification.');
        // For demo purposes, show local notification instead
        await _notificationService.showLocalNotification(
          title: senderName,
          body: messageContent,
          senderId: senderId,
          senderName: senderName,
          messageId: messageId,
        );
        return true;
      }

      // FCM v1 API message format
      final message = {
        'message': {
          'token': targetToken,
          'notification': {
            'title': senderName,
            'body': messageContent,
          },
          'data': {
            'messageId': messageId ?? DateTime.now().millisecondsSinceEpoch.toString(),
            'senderId': senderId,
            'receiverId': receiverId,
            'senderName': senderName,
            'messageContent': messageContent,
            'type': 'chat_message',
          },
          'android': {
            'priority': 'high',
            'notification': {
              'sound': 'default',
              'click_action': 'FLUTTER_NOTIFICATION_CLICK',
            },
          },
          'apns': {
            'payload': {
              'aps': {
                'sound': 'default',
                'content-available': 1,
              },
            },
          },
        },
      };

      final fcmUrl = 'https://fcm.googleapis.com/v1/projects/$_projectId/messages:send';
      final response = await http.post(
        Uri.parse(fcmUrl),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $accessToken',
        },
        body: jsonEncode(message),
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        print('FCM v1 notification sent successfully: $responseData');
        return true;
      } else {
        print('Failed to send FCM v1 notification: ${response.statusCode} - ${response.body}');
        // Fallback to local notification
        await _notificationService.showLocalNotification(
          title: senderName,
          body: messageContent,
          senderId: senderId,
          senderName: senderName,
          messageId: messageId,
        );
        return false;
      }
    } catch (e) {
      print('Error sending push notification: $e');
      // Fallback to local notification
      await _notificationService.showLocalNotification(
        title: senderName,
        body: messageContent,
        senderId: senderId,
        senderName: senderName,
        messageId: messageId,
      );
      return false;
    }
  }
  
  Future<void> subscribeToTopic(String topic) async {
    await _messaging.subscribeToTopic(topic);
  }
  
  Future<void> unsubscribeFromTopic(String topic) async {
    await _messaging.unsubscribeFromTopic(topic);
  }
  
  Future<String?> getDeviceToken() async {
    return await _messaging.getToken();
  }
  
  Future<void> refreshToken() async {
    _deviceToken = await _messaging.getToken();
    if (_deviceToken != null && _authService.currentUser != null) {
      await _authService.updateDeviceToken(_deviceToken!);
    }
  }
  
  // Listen to token refresh
  void listenToTokenRefresh() {
    _messaging.onTokenRefresh.listen((newToken) {
      _deviceToken = newToken;
      if (_authService.currentUser != null) {
        _authService.updateDeviceToken(newToken);
      }
    });
  }

  /// Send notification to a specific user by their user ID
  /// This method looks up the user's FCM token and sends the notification
  Future<bool> sendNotificationToUser({
    required String targetUserId,
    required String messageContent,
    String? messageId,
  }) async {
    try {
      // Get the target user's information including FCM token
      final targetUser = await _databaseService.getUserById(targetUserId);
      if (targetUser == null) {
        print('Target user not found: $targetUserId');
        return false;
      }

      if (targetUser.fcmToken == null || targetUser.fcmToken!.isEmpty) {
        print('Target user has no FCM token: $targetUserId');
        return false;
      }

      final currentUser = _authService.currentUser;
      if (currentUser == null) {
        print('Current user not found');
        return false;
      }

      // Send the notification
      return await sendPushNotification(
        targetToken: targetUser.fcmToken!,
        senderName: currentUser.displayName,
        messageContent: messageContent,
        senderId: currentUser.id!,
        receiverId: targetUserId,
        messageId: messageId,
      );
    } catch (e) {
      print('Error sending notification to user: $e');
      return false;
    }
  }

  /// Send notification to multiple users
  Future<List<bool>> sendNotificationToMultipleUsers({
    required List<String> targetUserIds,
    required String messageContent,
    String? messageId,
  }) async {
    final results = <bool>[];

    for (final userId in targetUserIds) {
      final result = await sendNotificationToUser(
        targetUserId: userId,
        messageContent: messageContent,
        messageId: messageId,
      );
      results.add(result);
    }

    return results;
  }

  /// Send notification to all users in a group/topic
  Future<bool> sendNotificationToTopic({
    required String topic,
    required String messageContent,
    String? messageId,
  }) async {
    try {
      final currentUser = _authService.currentUser;
      if (currentUser == null) {
        print('Current user not found');
        return false;
      }

      // Get OAuth 2.0 access token
      final accessToken = await _getAccessToken();
      if (accessToken == null) {
        print('Could not get access token for topic messaging.');
        return false;
      }

      // FCM v1 API message format for topics
      final message = {
        'message': {
          'topic': topic,
          'notification': {
            'title': currentUser.displayName,
            'body': messageContent,
          },
          'data': {
            'messageId': messageId ?? DateTime.now().millisecondsSinceEpoch.toString(),
            'senderId': currentUser.id!,
            'senderName': currentUser.displayName,
            'messageContent': messageContent,
            'type': 'group_message',
            'topic': topic,
          },
          'android': {
            'priority': 'high',
            'notification': {
              'sound': 'default',
              'click_action': 'FLUTTER_NOTIFICATION_CLICK',
            },
          },
          'apns': {
            'payload': {
              'aps': {
                'sound': 'default',
                'content-available': 1,
              },
            },
          },
        },
      };

      final fcmUrl = 'https://fcm.googleapis.com/v1/projects/$_projectId/messages:send';
      final response = await http.post(
        Uri.parse(fcmUrl),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $accessToken',
        },
        body: jsonEncode(message),
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        print('FCM v1 topic notification sent successfully: $responseData');
        return true;
      } else {
        print('Failed to send FCM v1 topic notification: ${response.statusCode} - ${response.body}');
        return false;
      }
    } catch (e) {
      print('Error sending topic notification: $e');
      return false;
    }
  }
}
