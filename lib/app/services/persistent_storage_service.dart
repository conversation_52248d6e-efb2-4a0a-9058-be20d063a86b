import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:get/get.dart';
import '../models/user_model.dart';
import '../utils/logger.dart';

/// Persistent Storage Service that survives app deletion and reinstallation
/// Uses iOS Keychain and Android Keystore for secure, persistent storage
class PersistentStorageService extends GetxService {
  static const String _userDataKey = 'persistent_user_data';
  static const String _deviceIdKey = 'persistent_device_id';
  static const String _userCredentialsKey = 'persistent_user_credentials';
  static const String _appInstallationIdKey = 'app_installation_id';
  
  late FlutterSecureStorage _secureStorage;
  late SharedPreferences _prefs;
  late DeviceInfoPlugin _deviceInfo;
  
  String? _deviceId;
  String? _installationId;
  
  String? get deviceId => _deviceId;
  String? get installationId => _installationId;
  
  Future<PersistentStorageService> init() async {
    try {
      // Initialize secure storage with platform-specific options
      _secureStorage = const FlutterSecureStorage(
        aOptions: AndroidOptions(
          encryptedSharedPreferences: true,
          // This ensures data survives app deletion on Android
          sharedPreferencesName: 'persistent_chat_app_data',
          preferencesKeyPrefix: 'persistent_',
        ),
        iOptions: IOSOptions(
          // Keychain data survives app deletion by default
          accessibility: KeychainAccessibility.first_unlock_this_device,
          synchronizable: false,
        ),
      );
      
      _prefs = await SharedPreferences.getInstance();
      _deviceInfo = DeviceInfoPlugin();
      
      // Generate or retrieve device ID
      await _initializeDeviceId();
      
      // Generate or retrieve installation ID
      await _initializeInstallationId();
      
      AppLogger.info('Persistent storage service initialized successfully');
      return this;
    } catch (e) {
      AppLogger.error('Failed to initialize persistent storage service', 'PersistentStorage', e);
      rethrow;
    }
  }
  
  /// Initialize device ID (unique per device, survives app deletion)
  Future<void> _initializeDeviceId() async {
    try {
      // Try to get existing device ID from secure storage
      _deviceId = await _secureStorage.read(key: _deviceIdKey);
      
      if (_deviceId == null) {
        // Generate device ID based on device information
        if (Platform.isAndroid) {
          final androidInfo = await _deviceInfo.androidInfo;
          _deviceId = _generateDeviceId(
            androidInfo.id,
            androidInfo.model,
            androidInfo.brand,
          );
        } else if (Platform.isIOS) {
          final iosInfo = await _deviceInfo.iosInfo;
          _deviceId = _generateDeviceId(
            iosInfo.identifierForVendor ?? 'unknown',
            iosInfo.model,
            iosInfo.systemName,
          );
        } else {
          _deviceId = _generateDeviceId('web', 'web', 'web');
        }
        
        // Store device ID securely
        await _secureStorage.write(key: _deviceIdKey, value: _deviceId);
        AppLogger.info('Generated new device ID: ${_deviceId?.substring(0, 8)}...');
      } else {
        AppLogger.info('Retrieved existing device ID: ${_deviceId?.substring(0, 8)}...');
      }
    } catch (e) {
      AppLogger.error('Error initializing device ID', 'PersistentStorage', e);
      _deviceId = 'fallback_${DateTime.now().millisecondsSinceEpoch}';
    }
  }
  
  /// Initialize installation ID (unique per app installation)
  Future<void> _initializeInstallationId() async {
    try {
      _installationId = _prefs.getString(_appInstallationIdKey);
      
      if (_installationId == null) {
        _installationId = 'install_${DateTime.now().millisecondsSinceEpoch}_${_deviceId?.substring(0, 8)}';
        await _prefs.setString(_appInstallationIdKey, _installationId!);
        AppLogger.info('Generated new installation ID: $_installationId');
      } else {
        AppLogger.info('Retrieved existing installation ID: $_installationId');
      }
    } catch (e) {
      AppLogger.error('Error initializing installation ID', 'PersistentStorage', e);
      _installationId = 'fallback_install_${DateTime.now().millisecondsSinceEpoch}';
    }
  }
  
  /// Generate device ID from device information
  String _generateDeviceId(String id, String model, String brand) {
    final combined = '$id-$model-$brand';
    final bytes = utf8.encode(combined);
    final hash = bytes.fold(0, (prev, element) => prev + element);
    return 'device_${hash.abs()}_${DateTime.now().millisecondsSinceEpoch}';
  }
  
  /// Save user data persistently (survives app deletion)
  Future<bool> saveUserData(UserModel user) async {
    try {
      final userData = {
        'id': user.id,
        'email': user.email,
        'displayName': user.displayName,
        'country': user.country,
        'mobile': user.mobile,
        'fcmToken': user.fcmToken,
        'createdAt': user.createdAt.toIso8601String(),
        'updatedAt': user.updatedAt.toIso8601String(),
        'deviceId': _deviceId,
        'installationId': _installationId,
        'savedAt': DateTime.now().toIso8601String(),
      };
      
      final userDataJson = json.encode(userData);
      await _secureStorage.write(key: _userDataKey, value: userDataJson);
      
      AppLogger.info('User data saved persistently for user: ${user.email}');
      return true;
    } catch (e) {
      AppLogger.error('Error saving user data persistently', 'PersistentStorage', e);
      return false;
    }
  }
  
  /// Retrieve user data (available after app reinstallation)
  Future<UserModel?> getUserData() async {
    try {
      final userDataJson = await _secureStorage.read(key: _userDataKey);
      
      if (userDataJson == null) {
        AppLogger.info('No persistent user data found');
        return null;
      }
      
      final userData = json.decode(userDataJson) as Map<String, dynamic>;
      
      // Verify device ID matches (security check)
      if (userData['deviceId'] != _deviceId) {
        AppLogger.warning('Device ID mismatch, clearing persistent data');
        await clearUserData();
        return null;
      }
      
      final user = UserModel(
        id: userData['id'] as String,
        email: userData['email'] as String,
        displayName: userData['displayName'] as String,
        country: userData['country'] as String,
        mobile: userData['mobile'] as String,
        fcmToken: userData['fcmToken'] as String?,
        createdAt: DateTime.parse(userData['createdAt'] as String),
        updatedAt: DateTime.parse(userData['updatedAt'] as String),
      );
      
      AppLogger.info('Retrieved persistent user data for: ${user.email}');
      return user;
    } catch (e) {
      AppLogger.error('Error retrieving user data', 'PersistentStorage', e);
      return null;
    }
  }
  
  /// Save user credentials securely (for auto-login)
  Future<bool> saveUserCredentials(String email, String hashedPassword) async {
    try {
      final credentials = {
        'email': email,
        'hashedPassword': hashedPassword,
        'deviceId': _deviceId,
        'savedAt': DateTime.now().toIso8601String(),
      };
      
      final credentialsJson = json.encode(credentials);
      await _secureStorage.write(key: _userCredentialsKey, value: credentialsJson);
      
      AppLogger.info('User credentials saved securely for: $email');
      return true;
    } catch (e) {
      AppLogger.error('Error saving user credentials', 'PersistentStorage', e);
      return false;
    }
  }
  
  /// Retrieve user credentials (for auto-login)
  Future<Map<String, String>?> getUserCredentials() async {
    try {
      final credentialsJson = await _secureStorage.read(key: _userCredentialsKey);
      
      if (credentialsJson == null) {
        AppLogger.info('No persistent credentials found');
        return null;
      }
      
      final credentials = json.decode(credentialsJson) as Map<String, dynamic>;
      
      // Verify device ID matches
      if (credentials['deviceId'] != _deviceId) {
        AppLogger.warning('Device ID mismatch for credentials, clearing data');
        await clearUserCredentials();
        return null;
      }
      
      return {
        'email': credentials['email'] as String,
        'hashedPassword': credentials['hashedPassword'] as String,
      };
    } catch (e) {
      AppLogger.error('Error retrieving user credentials', 'PersistentStorage', e);
      return null;
    }
  }
  
  /// Clear user data
  Future<void> clearUserData() async {
    try {
      await _secureStorage.delete(key: _userDataKey);
      AppLogger.info('Persistent user data cleared');
    } catch (e) {
      AppLogger.error('Error clearing user data', 'PersistentStorage', e);
    }
  }
  
  /// Clear user credentials
  Future<void> clearUserCredentials() async {
    try {
      await _secureStorage.delete(key: _userCredentialsKey);
      AppLogger.info('Persistent user credentials cleared');
    } catch (e) {
      AppLogger.error('Error clearing user credentials', 'PersistentStorage', e);
    }
  }
  
  /// Clear all persistent data
  Future<void> clearAllData() async {
    try {
      await _secureStorage.deleteAll();
      await _prefs.clear();
      AppLogger.info('All persistent data cleared');
    } catch (e) {
      AppLogger.error('Error clearing all persistent data', 'PersistentStorage', e);
    }
  }
  
  /// Check if user data exists
  Future<bool> hasUserData() async {
    try {
      final userData = await _secureStorage.read(key: _userDataKey);
      return userData != null;
    } catch (e) {
      return false;
    }
  }
  
  /// Get storage information for debugging
  Map<String, dynamic> getStorageInfo() {
    return {
      'deviceId': _deviceId,
      'installationId': _installationId,
      'platform': Platform.operatingSystem,
      'storageType': Platform.isIOS ? 'Keychain' : 'EncryptedSharedPreferences',
    };
  }
}
