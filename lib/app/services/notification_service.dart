import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter/services.dart';

class NotificationService extends GetxService {
  static const MethodChannel _channel = MethodChannel('notification_service');
  
  Future<NotificationService> init() async {
    await _setupNotificationChannel();
    return this;
  }
  
  Future<void> _setupNotificationChannel() async {
    try {
      // Setup notification channel for Android
      await _channel.invokeMethod('setupNotificationChannel', {
        'channelId': 'chat_messages',
        'channelName': 'Chat Messages',
        'channelDescription': 'Notifications for new chat messages',
        'importance': 'high',
      });
    } catch (e) {
      print('Error setting up notification channel: $e');
    }
  }
  
  Future<void> showLocalNotification({
    required String title,
    required String body,
    required String senderId,
    required String senderName,
    String? messageId,
    Map<String, dynamic>? data,
  }) async {
    try {
      await _channel.invokeMethod('showNotification', {
        'id': messageId?.hashCode ?? DateTime.now().millisecondsSinceEpoch,
        'title': title,
        'body': body,
        'channelId': 'chat_messages',
        'data': {
          'senderId': senderId,
          'senderName': senderName,
          'messageId': messageId,
          'type': 'chat_message',
          ...?data,
        },
      });
    } catch (e) {
      print('Error showing local notification: $e');
    }
  }
  
  Future<void> cancelNotification(int notificationId) async {
    try {
      await _channel.invokeMethod('cancelNotification', {
        'id': notificationId,
      });
    } catch (e) {
      print('Error canceling notification: $e');
    }
  }
  
  Future<void> cancelAllNotifications() async {
    try {
      await _channel.invokeMethod('cancelAllNotifications');
    } catch (e) {
      print('Error canceling all notifications: $e');
    }
  }
  
  // Show in-app notification when app is in foreground
  void showInAppNotification({
    required String title,
    required String message,
    required VoidCallback onTap,
    Duration duration = const Duration(seconds: 4),
  }) {
    Get.snackbar(
      title,
      message,
      snackPosition: SnackPosition.TOP,
      backgroundColor: Get.theme.primaryColor.withOpacity(0.9),
      colorText: Colors.white,
      duration: duration,
      margin: const EdgeInsets.all(16),
      borderRadius: 8,
      isDismissible: true,
      dismissDirection: DismissDirection.horizontal,
      forwardAnimationCurve: Curves.easeOutBack,
      reverseAnimationCurve: Curves.easeInBack,
      animationDuration: const Duration(milliseconds: 300),
      onTap: (_) => onTap(),
      mainButton: TextButton(
        onPressed: onTap,
        child: const Text(
          'View',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        ),
      ),
    );
  }
  
  // Show typing indicator notification
  void showTypingNotification(String userName) {
    Get.snackbar(
      userName,
      'typing'.tr,
      snackPosition: SnackPosition.TOP,
      backgroundColor: Colors.grey[600],
      colorText: Colors.white,
      duration: const Duration(seconds: 2),
      margin: const EdgeInsets.all(16),
      borderRadius: 8,
      isDismissible: true,
      animationDuration: const Duration(milliseconds: 200),
    );
  }
  
  // Show message status notifications
  void showMessageStatusNotification({
    required String status,
    required String recipientName,
  }) {
    String message;
    Color backgroundColor;
    
    switch (status.toLowerCase()) {
      case 'delivered':
        message = 'Message delivered to $recipientName';
        backgroundColor = Colors.green;
        break;
      case 'read':
        message = 'Message read by $recipientName';
        backgroundColor = Colors.blue;
        break;
      case 'failed':
        message = 'Failed to send message to $recipientName';
        backgroundColor = Colors.red;
        break;
      default:
        return;
    }
    
    Get.snackbar(
      'Message Status',
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: backgroundColor,
      colorText: Colors.white,
      duration: const Duration(seconds: 2),
      margin: const EdgeInsets.all(16),
      borderRadius: 8,
      isDismissible: true,
    );
  }
  
  // Handle notification permission
  Future<bool> requestNotificationPermission() async {
    try {
      final bool granted = await _channel.invokeMethod('requestPermission');
      return granted;
    } catch (e) {
      print('Error requesting notification permission: $e');
      return false;
    }
  }
  
  // Check if notifications are enabled
  Future<bool> areNotificationsEnabled() async {
    try {
      final bool enabled = await _channel.invokeMethod('areNotificationsEnabled');
      return enabled;
    } catch (e) {
      print('Error checking notification status: $e');
      return false;
    }
  }
  
  // Open notification settings
  Future<void> openNotificationSettings() async {
    try {
      await _channel.invokeMethod('openNotificationSettings');
    } catch (e) {
      print('Error opening notification settings: $e');
    }
  }
}
