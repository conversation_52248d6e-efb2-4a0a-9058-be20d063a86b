import 'package:get/get.dart';
import '../services/auth_service.dart';
import '../services/database_service.dart';
import '../services/firebase_service.dart';
import '../services/localization_service.dart';
import '../services/notification_service.dart';
import '../services/country_service.dart';

class InitialBinding extends Bindings {
  @override
  void dependencies() {
    // Core services are already initialized in main.dart
    // This binding ensures they are available throughout the app
    Get.lazyPut<AuthService>(() => Get.find<AuthService>());
    Get.lazyPut<DatabaseService>(() => Get.find<DatabaseService>());
    Get.lazyPut<CountryService>(() => Get.find<CountryService>());
    Get.lazyPut<NotificationService>(() => Get.find<NotificationService>());
    Get.lazyPut<FirebaseService>(() => Get.find<FirebaseService>());
    Get.lazyPut<LocalizationService>(() => Get.find<LocalizationService>());
  }
}
