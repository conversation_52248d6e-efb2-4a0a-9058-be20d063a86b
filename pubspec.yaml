name: project
description: "A comprehensive Flutter chat application with Firebase, Maps, and multi-language support."
publish_to: 'none'
version: 1.0.0

environment:
  sdk: ^3.0.0

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # State Management
  get: ^4.6.6

  # Firebase
  firebase_core: ^3.6.0
  firebase_messaging: ^15.1.3

  # Database
  sqflite: ^2.3.0
  shared_preferences: ^2.2.2

  # HTTP & API
  http: ^1.1.2
  dio: ^5.4.0

  # QR Code
  qr_flutter: ^4.1.0
  qr_code_scanner: ^1.0.1
  share_plus: ^7.2.1

  # Maps
  google_maps_flutter: ^2.5.0
  geolocator: ^10.1.0
  geocoding: ^2.1.1

  # UI Components
  flutter_svg: ^2.0.9
  cached_network_image: ^3.3.0
  image_picker: ^1.0.4

  # Internationalization
  intl: ^0.19.0

  # Permissions
  permission_handler: ^11.1.0

  # Utils
  path_provider: ^2.1.1
  path: ^1.9.0
  uuid: ^4.2.1
  crypto: ^3.0.3

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^4.0.0
  build_runner: ^2.4.7

flutter:
  uses-material-design: true

  # Assets
  assets:
    - assets/images/
    - assets/icons/
    - assets/lang/

  # Use system fonts for now
  # fonts:
  #   - family: Roboto
  #     fonts:
  #       - asset: assets/fonts/Roboto-Regular.ttf
  #       - asset: assets/fonts/Roboto-Bold.ttf
  #         weight: 700
