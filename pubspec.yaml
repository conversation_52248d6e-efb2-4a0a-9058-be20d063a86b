name: project
description: "A comprehensive Flutter chat application with Firebase, Maps, and multi-language support."
publish_to: 'none'
version: 1.0.0

environment:
  sdk: ^3.6.1

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # State Management
  get: ^4.6.6

  # Firebase
  firebase_core: ^2.24.2
  # firebase_messaging: ^14.6.9  # Temporarily disabled for iOS build issues

  # Database
  sqflite: ^2.3.0
  path: ^1.8.3
  shared_preferences: ^2.2.2

  # HTTP & API
  http: ^1.4.0
  dio: ^5.4.0

  # QR Code
  qr_flutter: ^4.1.0

  # Share Plus
  share_plus: ^7.2.1


  # Maps
  google_maps_flutter: ^2.5.0
  geolocator: ^10.1.0
  geocoding: ^2.1.1

  # UI Components
  flutter_svg: ^2.0.9
  cached_network_image: ^3.3.0
  image_picker: ^1.0.4

  # Internationalization
  intl: ^0.19.0

  # Permissions
  permission_handler: ^11.1.0

  # Utils
  path_provider: ^2.1.1
  uuid: ^4.2.1
  crypto: ^3.0.3
  mobile_scanner: ^5.0.0
  flutter_local_notifications: ^19.3.0
  googleapis_auth: ^2.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  build_runner: ^2.4.7
  change_app_package_name: ^1.5.0

flutter:
  uses-material-design: true

  # Assets
  assets:
    - assets/images/
    - assets/icons/
    - assets/lang/

  # Use system fonts for now
  # fonts:
  #   - family: Roboto
  #     fonts:
  #       - asset: assets/fonts/Roboto-Regular.ttf
  #       - asset: assets/fonts/Roboto-Bold.ttf
  #         weight: 700
