# 💬 Chat App - Flutter Multi-Platform Messaging Application

A comprehensive Flutter chat application with real-time messaging, QR code user connection, maps integration, and multi-language support.

## 🚀 Features

### ✅ Core Functionality
- **Real-time Messaging**: Text-based chat between users with Firebase Cloud Messaging
- **User Authentication**: Email/password login and registration with persistent sessions
- **QR Code Connection**: Share device tokens via QR codes to connect with other users
- **Maps Integration**: Google Maps with current location, 2km boundary, and water body highlighting
- **Multi-language Support**: English and Arabic with RTL layout support
- **Cross-platform**: Android, iOS, and Web support

### 🔧 Technical Features
- **State Management**: GetX for reactive state management
- **Local Database**: SQLite for offline message storage
- **Push Notifications**: Firebase Cloud Messaging for real-time notifications
- **Location Services**: Real-time location tracking and map visualization
- **Responsive Design**: Material Design 3 with adaptive layouts
- **Country Selection**: REST Countries API integration for registration

## 🏗️ Architecture

- **Pattern**: MVVM with GetX
- **State Management**: GetX (Reactive)
- **Database**: SQLite with sqflite
- **Backend**: Firebase (Authentication, Cloud Messaging)
- **Navigation**: GetX Navigation
- **Internationalization**: GetX Translations

## 📋 Requirements

### Development Environment
- Flutter SDK 3.22.2+
- Dart SDK 3.4.3+
- Android Studio / VS Code
- Git

### Platform Requirements
- **Android**: API level 21+ (Android 5.0+)
- **iOS**: iOS 12.0+
- **Web**: Modern browsers (Chrome, Firefox, Safari, Edge)

## 🛠️ Installation

### 1. Clone the Repository
```bash
git clone <repository-url>
cd project
```

### 2. Install Dependencies
```bash
flutter pub get
```

### 3. Configure Firebase
1. Create a Firebase project at [Firebase Console](https://console.firebase.google.com)
2. Add your platform apps (Android/iOS/Web)
3. Download configuration files:
   - `google-services.json` for Android → `android/app/`
   - `GoogleService-Info.plist` for iOS → `ios/Runner/`
   - Web config → Update `web/index.html`

### 4. Configure Google Maps
1. Get API key from [Google Cloud Console](https://console.cloud.google.com)
2. Enable Maps SDK for your platforms
3. Update API keys in:
   - `android/app/src/main/AndroidManifest.xml`
   - `ios/Runner/AppDelegate.swift`
   - `web/index.html`

### 5. Run the App
```bash
# Debug mode
flutter run

# Specific platform
flutter run -d android
flutter run -d ios
flutter run -d chrome
```

## 🏗️ Building for Production

### Android
```bash
# APK
flutter build apk --release

# App Bundle (recommended for Play Store)
flutter build appbundle --release
```

### iOS
```bash
flutter build ios --release
# Then archive in Xcode
```

### Web
```bash
flutter build web --release
```

## 📚 Key Dependencies

- **get**: State management and navigation
- **firebase_core & firebase_messaging**: Firebase integration
- **sqflite**: Local SQLite database
- **google_maps_flutter**: Maps integration
- **geolocator**: Location services
- **qr_flutter & qr_code_scanner**: QR code functionality
- **shared_preferences**: Local storage
- **http & dio**: Network requests
- **permission_handler**: Runtime permissions

## 🌍 Supported Languages

- 🇺🇸 English
- 🇸🇦 Arabic (with RTL support)

## 📖 Usage

### Getting Started
1. **Register**: Create an account with email, country, and display name
2. **Generate QR**: Create your QR code for device token sharing
3. **Connect**: Scan another user's QR code to connect
4. **Chat**: Start messaging with connected users
5. **Maps**: View your location and nearby water bodies
6. **Settings**: Change language, view profile, logout

### Key Features Usage
- **QR Connection**: Go to Profile → Generate QR or use the QR scanner
- **Maps**: Access from home screen to see location and boundaries
- **Language**: Change in Profile → Language Settings
- **Notifications**: Automatic push notifications for new messages

## 📄 Documentation

- [DEPLOYMENT.md](DEPLOYMENT.md) - Detailed deployment guide
- [BUILD_VERIFICATION.md](BUILD_VERIFICATION.md) - Implementation verification

## 🆘 Support

For support and questions:
- Check the deployment guide for detailed setup instructions
- Review build verification for implementation details
- Open an issue for bugs or feature requests

---

**Built with ❤️ using Flutter**
