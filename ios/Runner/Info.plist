<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>Project</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>project</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>

	<!-- Location permissions -->
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>This app needs location access to show your current location on the map and find nearby water bodies.</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>This app needs location access to show your current location on the map and find nearby water bodies.</string>

	<!-- Camera permission for QR code scanning -->
	<key>NSCameraUsageDescription</key>
	<string>This app needs camera access to scan QR codes for device token sharing.</string>

	<!-- Photo library permission -->
	<key>NSPhotoLibraryUsageDescription</key>
	<string>This app needs photo library access to select images for profile pictures.</string>

	<!-- Microphone permission (if needed for future features) -->
	<key>NSMicrophoneUsageDescription</key>
	<string>This app may need microphone access for voice messages.</string>

	<!-- Push notification support -->
	<key>UIBackgroundModes</key>
	<array>
		<string>remote-notification</string>
		<string>background-fetch</string>
	</array>

	<!-- Firebase App Check (optional) -->
	<key>FirebaseAppDelegateProxyEnabled</key>
	<false/>

</dict>
</plist>
