# Uncomment this line to define a global platform for your project
platform :ios, '14.0'

# CocoaPods analytics sends network stats synchronously affecting flutter build latency.
ENV['COCOAPODS_DISABLE_STATS'] = 'true'

# Workaround for Firebase iOS build issues
$FirebaseSDKVersion = '10.18.0'

project 'Runner', {
  'Debug' => :debug,
  'Profile' => :release,
  'Release' => :release,
}

def flutter_root
  generated_xcode_build_settings_path = File.expand_path(File.join('..', 'Flutter', 'Generated.xcconfig'), __FILE__)
  unless File.exist?(generated_xcode_build_settings_path)
    raise "#{generated_xcode_build_settings_path} must exist. If you're running pod install manually, make sure flutter pub get is executed first"
  end

  File.foreach(generated_xcode_build_settings_path) do |line|
    matches = line.match(/FLUTTER_ROOT\=(.*)/)
    return matches[1].strip if matches
  end
  raise "FLUTTER_ROOT not found in #{generated_xcode_build_settings_path}. Try deleting Generated.xcconfig, then run flutter pub get"
end

require File.expand_path(File.join('packages', 'flutter_tools', 'bin', 'podhelper'), flutter_root)

flutter_ios_podfile_setup

target 'Runner' do
  use_frameworks!
  use_modular_headers!

  flutter_install_all_ios_pods File.dirname(File.realpath(__FILE__))

  target 'RunnerTests' do
    inherit! :search_paths
  end
end

post_install do |installer|
  installer.pods_project.targets.each do |target|
    flutter_additional_ios_build_settings(target)

    # Fix for Firebase iOS build issues
    target.build_configurations.each do |config|
      config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '12.0'
      config.build_settings['ENABLE_BITCODE'] = 'NO'

      # Comprehensive fix for Firebase header issues
      config.build_settings['CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES'] = 'YES'
      config.build_settings['DEFINES_MODULE'] = 'YES'
      config.build_settings['SWIFT_INCLUDE_PATHS'] = '$(inherited) $(PODS_TARGET_SRCROOT)'

      # Specific fixes for Firebase Messaging
      if target.name == 'firebase_messaging'
        config.build_settings['OTHER_CFLAGS'] = '$(inherited) -DCOCOAPODS=1 -DOBJC_OLD_DISPATCH_PROTOTYPES=0 -fmodules'
        config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] = '$(inherited) COCOAPODS=1'
        config.build_settings['CLANG_MODULES_AUTOLINK'] = 'YES'
        config.build_settings['CLANG_ENABLE_MODULES'] = 'YES'
      end

      # General Firebase fixes
      if target.name.include?('firebase') || target.name.include?('Firebase')
        config.build_settings['OTHER_CFLAGS'] = '$(inherited) -DCOCOAPODS=1 -DOBJC_OLD_DISPATCH_PROTOTYPES=0'
        config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] = '$(inherited) COCOAPODS=1'
      end
    end
  end
end
